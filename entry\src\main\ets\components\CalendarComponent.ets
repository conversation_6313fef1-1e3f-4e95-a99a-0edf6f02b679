import { Constants } from '../common/Constants';
import { DateUtils } from '../utils/DateUtils';

/**
 * 日历组件
 */
@Component
export struct CalendarComponent {
  @State currentYear: number = new Date().getFullYear();
  @State currentMonth: number = new Date().getMonth() + 1;
  @State selectedDate: string = DateUtils.getToday();
  onDateSelect?: (date: string) => void;

  private weekdays = ['日', '一', '二', '三', '四', '五', '六'];

  build() {
    Column() {
      // 月份导航
      Row() {
        Text('←')
          .fontSize(20)
          .fontColor(Constants.TEXT_PRIMARY)
          .onClick(() => {
            this.previousMonth();
          })

        Text(`${this.currentYear}年${this.currentMonth}月`)
          .fontSize(Constants.FONT_SIZE_TITLE)
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.TEXT_PRIMARY)
          .layoutWeight(1)
          .textAlign(TextAlign.Center)

        Text('→')
          .fontSize(20)
          .fontColor(Constants.TEXT_PRIMARY)
          .onClick(() => {
            this.nextMonth();
          })
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)
      .padding(Constants.PADDING_MEDIUM)

      // 星期标题
      Row() {
        ForEach(this.weekdays, (day: string) => {
          Text(day)
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_SECONDARY)
            .width('14.28%')
            .textAlign(TextAlign.Center)
        })
      }
      .width('100%')
      .padding({ bottom: Constants.PADDING_SMALL })

      // 日期网格
      this.buildCalendarGrid()
    }
    .width('100%')
    .backgroundColor(Constants.WHITE)
    .borderRadius(Constants.CARD_BORDER_RADIUS)
  }

  @Builder
  buildCalendarGrid() {
    Column() {
      ForEach(this.getCalendarWeeks(), (week: (number | null)[]) => {
        Row() {
          ForEach(week, (day: number | null) => {
            if (day) {
              this.buildDateCell(day)
            } else {
              Text('')
                .width('14.28%')
                .height(40)
            }
          })
        }
        .width('100%')
      })
    }
    .width('100%')
  }

  @Builder
  buildDateCell(day: number) {
    const dateStr = `${this.currentYear}-${String(this.currentMonth).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const isToday = DateUtils.isToday(dateStr);
    const isSelected = dateStr === this.selectedDate;

    Text(String(day))
      .fontSize(Constants.FONT_SIZE_MEDIUM)
      .fontColor(isSelected ? Constants.WHITE : (isToday ? Constants.PRIMARY_COLOR : Constants.TEXT_PRIMARY))
      .width('14.28%')
      .height(40)
      .textAlign(TextAlign.Center)
      .backgroundColor(isSelected ? Constants.PRIMARY_COLOR : (isToday ? '#E6F7FF' : 'transparent'))
      .borderRadius(isSelected || isToday ? 20 : 0)
      .onClick(() => {
        this.selectedDate = dateStr;
        if (this.onDateSelect) {
          this.onDateSelect(dateStr);
        }
      })
  }

  private getCalendarWeeks(): (number | null)[][] {
    const daysInMonth = DateUtils.getDaysInMonth(this.currentYear, this.currentMonth);
    const firstDayOfWeek = DateUtils.getFirstDayOfMonth(this.currentYear, this.currentMonth);
    
    const weeks: (number | null)[][] = [];
    let currentWeek: (number | null)[] = [];
    
    // 填充月初空白
    for (let i = 0; i < firstDayOfWeek; i++) {
      currentWeek.push(null);
    }
    
    // 填充日期
    for (let day = 1; day <= daysInMonth; day++) {
      currentWeek.push(day);
      
      if (currentWeek.length === 7) {
        weeks.push(currentWeek);
        currentWeek = [];
      }
    }
    
    // 填充月末空白
    while (currentWeek.length < 7 && currentWeek.length > 0) {
      currentWeek.push(null);
    }
    
    if (currentWeek.length > 0) {
      weeks.push(currentWeek);
    }
    
    return weeks;
  }

  private previousMonth() {
    if (this.currentMonth === 1) {
      this.currentMonth = 12;
      this.currentYear--;
    } else {
      this.currentMonth--;
    }
  }

  private nextMonth() {
    if (this.currentMonth === 12) {
      this.currentMonth = 1;
      this.currentYear++;
    } else {
      this.currentMonth++;
    }
  }
}
