/**
 * 健康数据模型定义
 */

// 用户信息模型
export interface UserInfo {
  id: string;
  name: string;
  avatar: string;
  age: number;
  gender: string;
  phone?: string;
}

// 家庭成员模型
export interface FamilyMember {
  id: string;
  name: string;
  avatar: string;
  relationship: string;
  age: number;
  healthStatus: string;
}

// 健康指标模型
export interface HealthMetric {
  type: string; // 血压、心率、体重等
  value: string;
  unit: string;
  date: string;
  status: 'normal' | 'warning' | 'danger';
}

// 健康任务模型
export interface HealthTask {
  id: string;
  title: string;
  description: string;
  type: string;
  time: string;
  completed: boolean;
  icon: string;
}

// 体感操作模型
export interface BodySenseAction {
  id: string;
  title: string;
  icon: string;
  color: string;
}

// 健康提醒模型
export interface HealthReminder {
  id: string;
  title: string;
  content: string;
  type: 'warning' | 'info' | 'success';
  time: string;
}

// 健康数据统计模型
export interface HealthStats {
  type: string;
  value: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  data: number[];
  labels: string[];
}

// 健康动态模型
export interface HealthActivity {
  id: string;
  title: string;
  content: string;
  time: string;
  type: string;
  icon: string;
}

// 日历事件模型
export interface CalendarEvent {
  id: string;
  title: string;
  date: string;
  time: string;
  type: string;
  status: 'pending' | 'completed' | 'cancelled';
}

// 智能助手消息模型
export interface AssistantMessage {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  time: string;
  isChart?: boolean;
  chartData?: any;
}

// 健康报告模型
export interface HealthReport {
  id: string;
  title: string;
  summary: string;
  score: number;
  date: string;
  details: HealthReportDetail[];
}

export interface HealthReportDetail {
  metric: string;
  value: string;
  status: 'good' | 'warning' | 'poor';
  suggestion: string;
}

// 设置项模型
export interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  type: 'navigation' | 'switch' | 'action';
  value?: boolean;
}
