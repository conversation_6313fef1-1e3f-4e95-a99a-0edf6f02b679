import { Constants } from './Constants';

/**
 * 通用样式定义
 */
export class Styles {
  // 卡片样式
  static cardStyle() {
    return {
      backgroundColor: Constants.WHITE,
      borderRadius: Constants.CARD_BORDER_RADIUS,
      padding: Constants.PADDING_MEDIUM,
      margin: Constants.MARGIN_SMALL,
      shadow: {
        radius: 4,
        color: '#00000010',
        offsetX: 0,
        offsetY: 2
      }
    };
  }

  // 按钮样式
  static primaryButtonStyle() {
    return {
      backgroundColor: Constants.PRIMARY_COLOR,
      borderRadius: Constants.BORDER_RADIUS,
      padding: {
        top: 12,
        bottom: 12,
        left: 24,
        right: 24
      }
    };
  }

  static secondaryButtonStyle() {
    return {
      backgroundColor: Constants.WHITE,
      borderRadius: Constants.BORDER_RADIUS,
      border: {
        width: 1,
        color: Constants.BORDER_COLOR
      },
      padding: {
        top: 12,
        bottom: 12,
        left: 24,
        right: 24
      }
    };
  }

  // 文本样式
  static titleTextStyle() {
    return {
      fontSize: Constants.FONT_SIZE_TITLE,
      fontWeight: FontWeight.Bold,
      fontColor: Constants.TEXT_PRIMARY
    };
  }

  static bodyTextStyle() {
    return {
      fontSize: Constants.FONT_SIZE_MEDIUM,
      fontColor: Constants.TEXT_PRIMARY
    };
  }

  static captionTextStyle() {
    return {
      fontSize: Constants.FONT_SIZE_SMALL,
      fontColor: Constants.TEXT_SECONDARY
    };
  }

  // 页面容器样式
  static pageContainerStyle() {
    return {
      width: '100%',
      height: '100%',
      backgroundColor: Constants.BACKGROUND_COLOR
    };
  }

  // 头部样式
  static headerStyle() {
    return {
      width: '100%',
      height: 56,
      backgroundColor: Constants.WHITE,
      padding: {
        left: Constants.PADDING_MEDIUM,
        right: Constants.PADDING_MEDIUM
      },
      justifyContent: FlexAlign.SpaceBetween,
      alignItems: ItemAlign.Center
    };
  }

  // 底部导航栏样式
  static tabBarStyle() {
    return {
      width: '100%',
      height: Constants.TAB_BAR_HEIGHT,
      backgroundColor: Constants.WHITE,
      border: {
        width: { top: 1 },
        color: Constants.BORDER_COLOR
      }
    };
  }

  // 图标样式
  static iconStyle(size: number = Constants.TAB_ICON_SIZE) {
    return {
      width: size,
      height: size
    };
  }

  // 分割线样式
  static dividerStyle() {
    return {
      width: '100%',
      height: 1,
      backgroundColor: Constants.BORDER_COLOR
    };
  }
}
