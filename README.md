# 鸿蒙健康管家移动端项目

## 项目概述

这是一个基于鸿蒙操作系统开发的健康管家移动端应用，提供家庭健康管理、健康数据追踪、智能健康助手等功能。

## 功能特性

### 🏠 首页（健康管家主页）
- 健康提醒通知
- 家庭成员健康状况概览
- 体感操作快捷入口
- 今日健康任务管理
- 家庭健康数据统计图表

### 📋 档案页面
- 个人档案管理
- 健康档案中心
- 个人药箱管理
- 综合健康概览
- 紧急联系人设置
- 健康数据概览（血压、心率等）
- 最新健康动态

### 📅 日历页面
- 健康日历组件
- 今日/本月健康任务
- 健康活动追踪
- 家庭活动概览
- 运动数据统计

### 🤖 智能助手页面
- AI对话界面
- 健康分析报告
- 血压趋势分析
- 体感咨询服务
- 健康建议推荐

### 👤 我的页面
- 用户信息管理
- 账户与服务设置
- 家庭成员管理
- 消息通知设置
- 帮助与反馈

## 项目结构

```
entry/src/main/ets/
├── common/                 # 公共模块
│   ├── Constants.ets      # 常量定义
│   └── Styles.ets         # 样式定义
├── components/            # 通用组件
│   ├── HeaderComponent.ets      # 头部组件
│   ├── CardComponent.ets        # 卡片组件
│   ├── CalendarComponent.ets    # 日历组件
│   ├── ChartComponent.ets       # 图表组件
│   ├── HealthMetricCard.ets     # 健康指标卡片
│   └── TaskItemComponent.ets    # 任务项组件
├── models/                # 数据模型
│   └── HealthModels.ets   # 健康相关数据模型
├── pages/                 # 页面组件
│   ├── Index.ets          # 主入口页面（含底部导航）
│   ├── HomePage.ets       # 首页
│   ├── ArchivePage.ets    # 档案页面
│   ├── CalendarPage.ets   # 日历页面
│   ├── AssistantPage.ets  # 智能助手页面
│   └── ProfilePage.ets    # 我的页面
└── utils/                 # 工具类
    ├── DateUtils.ets      # 日期工具
    └── DataUtils.ets      # 数据工具（模拟数据）
```

## 技术特点

### 🎨 UI设计
- 遵循鸿蒙设计规范
- 统一的颜色主题和字体规范
- 响应式布局设计
- 卡片式信息展示

### 🔧 架构设计
- 模块化组件架构
- 统一的样式管理
- 数据模型抽象
- 工具类封装

### 📱 交互体验
- 底部导航栏切换
- 流畅的页面滚动
- 直观的操作反馈
- 友好的用户界面

## 主要组件说明

### HeaderComponent
通用头部组件，支持标题、返回按钮、操作按钮等配置。

### CardComponent
通用卡片组件，提供统一的卡片样式和布局。

### CalendarComponent
日历组件，支持日期选择和事件显示。

### ChartComponent
简单图表组件，用于显示健康数据趋势。

### TaskItemComponent
任务项组件，支持任务状态切换。

## 数据模型

项目定义了完整的健康数据模型：
- UserInfo: 用户信息
- FamilyMember: 家庭成员
- HealthTask: 健康任务
- HealthMetric: 健康指标
- CalendarEvent: 日历事件
- AssistantMessage: 助手消息

## 开发规范

### 命名规范
- 文件名使用PascalCase
- 组件名使用PascalCase
- 变量名使用camelCase
- 常量使用UPPER_SNAKE_CASE

### 代码组织
- 按功能模块组织代码
- 统一的导入顺序
- 清晰的注释说明
- 合理的代码分层

## 运行说明

1. 确保已安装鸿蒙开发环境
2. 打开DevEco Studio
3. 导入项目
4. 连接鸿蒙设备或启动模拟器
5. 点击运行按钮

## 后续优化建议

1. **数据持久化**: 集成本地数据库存储
2. **网络请求**: 添加HTTP请求模块
3. **状态管理**: 引入状态管理框架
4. **动画效果**: 增加页面切换动画
5. **国际化**: 支持多语言
6. **主题切换**: 支持深色模式
7. **性能优化**: 列表虚拟化、图片懒加载等
8. **测试覆盖**: 添加单元测试和集成测试

## 注意事项

- 当前使用模拟数据，实际开发需要对接真实API
- 图片资源使用emoji占位，需要替换为实际图标
- 部分交互功能为演示性质，需要完善业务逻辑
- 建议根据实际需求调整UI样式和交互流程
