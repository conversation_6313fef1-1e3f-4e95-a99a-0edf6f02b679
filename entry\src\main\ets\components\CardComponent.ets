import { Constants } from '../common/Constants';

/**
 * 通用卡片组件
 */
@Component
export struct CardComponent {
  @Prop title: string = '';
  @Prop showTitle: boolean = true;
  @Prop showAction: boolean = false;
  @Prop actionText: string = '更多';
  @Prop padding: number = Constants.PADDING_MEDIUM;
  @Prop margin: number = Constants.MARGIN_SMALL;
  @Prop backgroundColor: string = Constants.WHITE;
  @BuilderParam content: () => void;
  onActionClick?: () => void;

  build() {
    Column() {
      // 卡片头部
      if (this.showTitle) {
        Row() {
          Text(this.title)
            .fontSize(Constants.FONT_SIZE_TITLE)
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.TEXT_PRIMARY)
            .layoutWeight(1)

          if (this.showAction) {
            Text(this.actionText)
              .fontSize(Constants.FONT_SIZE_MEDIUM)
              .fontColor(Constants.PRIMARY_COLOR)
              .onClick(() => {
                if (this.onActionClick) {
                  this.onActionClick();
                }
              })
          }
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .alignItems(VerticalAlign.Center)
        .margin({ bottom: Constants.MARGIN_SMALL })
      }

      // 卡片内容
      this.content()
    }
    .width('100%')
    .padding(this.padding)
    .margin(this.margin)
    .backgroundColor(this.backgroundColor)
    .borderRadius(Constants.CARD_BORDER_RADIUS)
    .shadow({
      radius: 4,
      color: '#00000010',
      offsetX: 0,
      offsetY: 2
    })
  }
}
