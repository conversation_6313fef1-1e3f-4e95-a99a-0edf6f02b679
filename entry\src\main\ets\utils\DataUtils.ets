import { 
  UserInfo, 
  FamilyMember, 
  HealthTask, 
  BodySenseAction, 
  HealthReminder,
  HealthStats,
  HealthActivity,
  CalendarEvent,
  SettingItem
} from '../models/HealthModels';

/**
 * 模拟数据工具类
 */
export class DataUtils {
  /**
   * 获取当前用户信息
   */
  static getCurrentUser(): UserInfo {
    return {
      id: '1',
      name: '王小明',
      avatar: '',
      age: 28,
      gender: '男',
      phone: '138****5421'
    };
  }

  /**
   * 获取家庭成员列表
   */
  static getFamilyMembers(): FamilyMember[] {
    return [
      {
        id: '1',
        name: '爸爸',
        avatar: '',
        relationship: '父亲',
        age: 55,
        healthStatus: 'normal'
      },
      {
        id: '2',
        name: '妈妈',
        avatar: '',
        relationship: '母亲',
        age: 52,
        healthStatus: 'warning'
      }
    ];
  }

  /**
   * 获取健康提醒
   */
  static getHealthReminders(): HealthReminder[] {
    return [
      {
        id: '1',
        title: '体检提醒',
        content: '今年的体检预约，建议尽快预约',
        type: 'warning',
        time: '09:00'
      }
    ];
  }

  /**
   * 获取体感操作列表
   */
  static getBodySenseActions(): BodySenseAction[] {
    return [
      {
        id: '1',
        title: '关节健康',
        icon: '',
        color: '#1890FF'
      },
      {
        id: '2',
        title: '水分补充计算',
        icon: '',
        color: '#52C41A'
      },
      {
        id: '3',
        title: '身体指标',
        icon: '',
        color: '#FA8C16'
      },
      {
        id: '4',
        title: '智能问诊',
        icon: '',
        color: '#722ED1'
      }
    ];
  }

  /**
   * 获取今日健康任务
   */
  static getTodayHealthTasks(): HealthTask[] {
    return [
      {
        id: '1',
        title: '服用维生素',
        description: '早餐·每日三次·09:00',
        type: 'medicine',
        time: '09:00',
        completed: false,
        icon: ''
      },
      {
        id: '2',
        title: '血压测量',
        description: '早餐·每日测量·10:30',
        type: 'measurement',
        time: '10:30',
        completed: false,
        icon: ''
      },
      {
        id: '3',
        title: '每日运动',
        description: '全家人·每日一次·全天',
        type: 'exercise',
        time: '全天',
        completed: true,
        icon: ''
      }
    ];
  }

  /**
   * 获取家庭健康统计数据
   */
  static getHealthStats(): HealthStats {
    return {
      type: '本周健康指数',
      value: 85,
      unit: '/100',
      trend: 'up',
      data: [75, 78, 82, 80, 85, 88, 85],
      labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    };
  }

  /**
   * 获取健康动态列表
   */
  static getHealthActivities(): HealthActivity[] {
    return [
      {
        id: '1',
        title: '最新测量数据',
        content: '血压 145/90 mmHg，心率 320千卡',
        time: '2023-07-15',
        type: 'measurement',
        icon: ''
      },
      {
        id: '2',
        title: '血压趋势',
        content: '今日测量：血压 118/75 mmHg，心率 7,845',
        time: '今日测量',
        type: 'trend',
        icon: ''
      },
      {
        id: '3',
        title: '最新健康动态',
        content: '今日测量：血压 118/75 mmHg，心率 7,845',
        time: '今日测量',
        type: 'activity',
        icon: ''
      },
      {
        id: '4',
        title: '下周二门诊预约',
        content: '北京协和医院 李医生 上午9:00',
        time: '7月18日 09:30',
        type: 'appointment',
        icon: ''
      }
    ];
  }

  /**
   * 获取日历事件
   */
  static getCalendarEvents(): CalendarEvent[] {
    return [
      {
        id: '1',
        title: '服用维生素',
        date: '2023-11-11',
        time: '09:00',
        type: 'medicine',
        status: 'completed'
      },
      {
        id: '2',
        title: '血压测量',
        date: '2023-11-11',
        time: '10:30',
        type: 'measurement',
        status: 'pending'
      },
      {
        id: '3',
        title: '每日运动',
        date: '2023-11-11',
        time: '全天',
        type: 'exercise',
        status: 'pending'
      },
      {
        id: '4',
        title: '白内障',
        date: '2023-11-11',
        time: '全天',
        type: 'condition',
        status: 'pending'
      }
    ];
  }

  /**
   * 获取设置项列表
   */
  static getSettingItems(): SettingItem[] {
    return [
      {
        id: '1',
        title: '我的家庭',
        subtitle: '管理家庭成员信息',
        icon: '',
        type: 'navigation'
      },
      {
        id: '2',
        title: '消息通知',
        subtitle: '各类系统通知设置',
        icon: '',
        type: 'navigation'
      },
      {
        id: '3',
        title: '我的健康报告',
        subtitle: '查看与管理个人健康报告',
        icon: '',
        type: 'navigation'
      },
      {
        id: '4',
        title: '我的收藏',
        subtitle: '查看已收藏的健康内容',
        icon: '',
        type: 'navigation'
      },
      {
        id: '5',
        title: '设置',
        subtitle: '通用设置、隐私与安全',
        icon: '',
        type: 'navigation'
      },
      {
        id: '6',
        title: '帮助与反馈',
        subtitle: '常见问题、联系客服',
        icon: '',
        type: 'navigation'
      }
    ];
  }
}
