import { Constants } from '../common/Constants';
import { HeaderComponent } from '../components/HeaderComponent';
import { CardComponent } from '../components/CardComponent';
import { ChartComponent } from '../components/ChartComponent';
import { DataUtils } from '../utils/DataUtils';
import { DateUtils } from '../utils/DateUtils';
import { 
  UserInfo, 
  FamilyMember, 
  HealthActivity,
  HealthStats
} from '../models/HealthModels';

/**
 * 档案页面
 */
@Component
export struct ArchivePage {
  @State currentUser: UserInfo = {} as UserInfo;
  @State familyMembers: FamilyMember[] = [];
  @State healthActivities: HealthActivity[] = [];
  @State selectedMemberIndex: number = 0;

  aboutToAppear() {
    this.loadData();
  }

  build() {
    Column() {
      // 头部
      HeaderComponent({
        title: '档案',
        showAction: true,
        actionIcon: '🔍',
        onActionClick: () => {
          // 搜索功能
        }
      })

      // 滚动内容
      Scroll() {
        Column() {
          // 家庭成员选择
          this.buildMemberSelector()

          // 个人档案卡片
          this.buildPersonalProfile()

          // 健康数据概览
          this.buildHealthDataOverview()

          // 最新健康动态
          this.buildHealthActivities()
        }
        .width('100%')
        .padding({ bottom: 20 })
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.BACKGROUND_COLOR)
  }

  @Builder
  buildMemberSelector() {
    Row() {
      ForEach([this.currentUser, ...this.familyMembers], (member: UserInfo | FamilyMember, index: number) => {
        Column() {
          // 头像
          Text('👤')
            .fontSize(24)
            .width(48)
            .height(48)
            .textAlign(TextAlign.Center)
            .backgroundColor(this.selectedMemberIndex === index ? Constants.PRIMARY_COLOR : '#F0F0F0')
            .fontColor(this.selectedMemberIndex === index ? Constants.WHITE : Constants.TEXT_SECONDARY)
            .borderRadius(24)

          Text(member.name)
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(this.selectedMemberIndex === index ? Constants.PRIMARY_COLOR : Constants.TEXT_PRIMARY)
            .margin({ top: 4 })
        }
        .margin({ right: Constants.MARGIN_MEDIUM })
        .onClick(() => {
          this.selectedMemberIndex = index;
        })
      })
    }
    .width('100%')
    .padding(Constants.PADDING_MEDIUM)
    .backgroundColor(Constants.WHITE)
    .margin({ bottom: Constants.MARGIN_SMALL })
  }

  @Builder
  buildPersonalProfile() {
    CardComponent({
      title: '个人档案',
      content: () => {
        Row() {
          // 左侧信息
          Column() {
            Row() {
              Text('🔵')
                .fontSize(16)
                .margin({ right: 8 })

              Text('个人档案')
                .fontSize(Constants.FONT_SIZE_MEDIUM)
                .fontColor(Constants.TEXT_PRIMARY)
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)

            Text('基本信息与健康档案')
              .fontSize(Constants.FONT_SIZE_SMALL)
              .fontColor(Constants.TEXT_SECONDARY)
              .margin({ top: 4 })
              .width('100%')
              .textAlign(TextAlign.Start)
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)

          // 右侧信息
          Column() {
            Row() {
              Text('🔵')
                .fontSize(16)
                .margin({ right: 8 })

              Text('健康档案中心')
                .fontSize(Constants.FONT_SIZE_MEDIUM)
                .fontColor(Constants.TEXT_PRIMARY)
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)

            Text('查看历史健康记录')
              .fontSize(Constants.FONT_SIZE_SMALL)
              .fontColor(Constants.TEXT_SECONDARY)
              .margin({ top: 4 })
              .width('100%')
              .textAlign(TextAlign.Start)
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)

        Divider()
          .color(Constants.BORDER_COLOR)
          .margin({ top: Constants.MARGIN_MEDIUM, bottom: Constants.MARGIN_MEDIUM })

        Row() {
          // 左侧信息
          Column() {
            Row() {
              Text('🔵')
                .fontSize(16)
                .margin({ right: 8 })

              Text('个人药箱')
                .fontSize(Constants.FONT_SIZE_MEDIUM)
                .fontColor(Constants.TEXT_PRIMARY)
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)

            Text('用药管理与提醒')
              .fontSize(Constants.FONT_SIZE_SMALL)
              .fontColor(Constants.TEXT_SECONDARY)
              .margin({ top: 4 })
              .width('100%')
              .textAlign(TextAlign.Start)
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)

          // 右侧信息
          Column() {
            Row() {
              Text('🔵')
                .fontSize(16)
                .margin({ right: 8 })

              Text('综合健康概览')
                .fontSize(Constants.FONT_SIZE_MEDIUM)
                .fontColor(Constants.TEXT_PRIMARY)
            }
            .width('100%')
            .alignItems(VerticalAlign.Center)

            Text('查看健康趋势与分析')
              .fontSize(Constants.FONT_SIZE_SMALL)
              .fontColor(Constants.TEXT_SECONDARY)
              .margin({ top: 4 })
              .width('100%')
              .textAlign(TextAlign.Start)
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)

        // 紧急联系人
        Row() {
          Text('🚨')
            .fontSize(16)
            .margin({ right: 8 })

          Text('紧急联系人')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.TEXT_PRIMARY)
            .layoutWeight(1)

          Text('未设置')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.ERROR_COLOR)
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ top: Constants.MARGIN_MEDIUM })
        .padding(Constants.PADDING_SMALL)
        .backgroundColor('#FFF2F0')
        .borderRadius(Constants.BORDER_RADIUS)
      }
    })
  }

  @Builder
  buildHealthDataOverview() {
    CardComponent({
      title: '健康数据概览',
      content: () => {
        Column() {
          // 最新测量数据标题
          Row() {
            Text('最新测量数据')
              .fontSize(Constants.FONT_SIZE_MEDIUM)
              .fontWeight(FontWeight.Medium)
              .fontColor(Constants.TEXT_PRIMARY)
              .layoutWeight(1)

            Text('2023-07-15')
              .fontSize(Constants.FONT_SIZE_SMALL)
              .fontColor(Constants.TEXT_SECONDARY)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .margin({ bottom: Constants.MARGIN_MEDIUM })

          // 健康指标
          Row() {
            Column() {
              Text('血压')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('145/90')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.TEXT_PRIMARY)
                .margin({ top: 4 })

              Text('mmHg')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)

            Column() {
              Text('心率')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('320')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.TEXT_PRIMARY)
                .margin({ top: 4 })

              Text('千卡')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)

            Column() {
              Text('心率')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('72')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.TEXT_PRIMARY)
                .margin({ top: 4 })

              Text('bpm')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)
          }
          .width('100%')

          // 血压趋势图
          Column() {
            Text('血压趋势')
              .fontSize(Constants.FONT_SIZE_MEDIUM)
              .fontWeight(FontWeight.Medium)
              .fontColor(Constants.TEXT_PRIMARY)
              .width('100%')
              .textAlign(TextAlign.Start)
              .margin({ top: Constants.MARGIN_LARGE, bottom: Constants.MARGIN_SMALL })

            // 简单的趋势线图表示
            Row() {
              ForEach([150, 145, 140, 138, 142, 145, 148], (value: number, index: number) => {
                Column() {
                  Text('●')
                    .fontSize(8)
                    .fontColor(index < 3 ? Constants.ERROR_COLOR : Constants.PRIMARY_COLOR)

                  Text(String(value))
                    .fontSize(Constants.FONT_SIZE_SMALL)
                    .fontColor(Constants.TEXT_SECONDARY)
                    .margin({ top: 4 })
                }
                .layoutWeight(1)
                .alignItems(HorizontalAlign.Center)
              })
            }
            .width('100%')
            .height(60)
            .alignItems(VerticalAlign.Bottom)

            Row() {
              ForEach(['6/30', '7/1', '7/2', '7/3', '7/4', '7/5', '7/6'], (label: string) => {
                Text(label)
                  .fontSize(Constants.FONT_SIZE_SMALL)
                  .fontColor(Constants.TEXT_SECONDARY)
                  .layoutWeight(1)
                  .textAlign(TextAlign.Center)
              })
            }
            .width('100%')
            .margin({ top: 4 })
          }
          .width('100%')
        }
        .width('100%')
      }
    })
  }

  @Builder
  buildHealthActivities() {
    CardComponent({
      title: '最新健康动态',
      showAction: true,
      actionText: '全部',
      content: () => {
        Column() {
          ForEach(this.healthActivities.slice(0, 3), (activity: HealthActivity, index: number) => {
            Row() {
              Text('🔵')
                .fontSize(16)
                .margin({ right: Constants.MARGIN_SMALL })

              Column() {
                Text(activity.title)
                  .fontSize(Constants.FONT_SIZE_MEDIUM)
                  .fontWeight(FontWeight.Medium)
                  .fontColor(Constants.TEXT_PRIMARY)
                  .width('100%')
                  .textAlign(TextAlign.Start)

                Text(activity.content)
                  .fontSize(Constants.FONT_SIZE_SMALL)
                  .fontColor(Constants.TEXT_SECONDARY)
                  .width('100%')
                  .textAlign(TextAlign.Start)
                  .margin({ top: 2 })

                Text(activity.time)
                  .fontSize(Constants.FONT_SIZE_SMALL)
                  .fontColor(Constants.TEXT_SECONDARY)
                  .width('100%')
                  .textAlign(TextAlign.Start)
                  .margin({ top: 4 })
              }
              .layoutWeight(1)
              .alignItems(HorizontalAlign.Start)
            }
            .width('100%')
            .alignItems(VerticalAlign.Top)
            .margin({ bottom: index < 2 ? Constants.MARGIN_MEDIUM : 0 })
          })
        }
        .width('100%')
      },
      onActionClick: () => {
        // 查看全部动态
      }
    })
  }

  private loadData() {
    this.currentUser = DataUtils.getCurrentUser();
    this.familyMembers = DataUtils.getFamilyMembers();
    this.healthActivities = DataUtils.getHealthActivities();
  }
}
