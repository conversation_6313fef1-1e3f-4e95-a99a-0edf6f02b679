import { Constants } from '../common/Constants';
import { HeaderComponent } from '../components/HeaderComponent';
import { CardComponent } from '../components/CardComponent';
import { HealthMetricCard } from '../components/HealthMetricCard';
import { TaskItemComponent } from '../components/TaskItemComponent';
import { ChartComponent } from '../components/ChartComponent';
import { DataUtils } from '../utils/DataUtils';
import { 
  FamilyMember, 
  HealthTask, 
  BodySenseAction, 
  HealthReminder,
  HealthStats
} from '../models/HealthModels';

/**
 * 健康管家首页
 */
@Component
export struct HomePage {
  @State familyMembers: FamilyMember[] = [];
  @State healthTasks: HealthTask[] = [];
  @State bodySenseActions: BodySenseAction[] = [];
  @State healthReminders: HealthReminder[] = [];
  @State healthStats: HealthStats = {} as HealthStats;

  aboutToAppear() {
    this.loadData();
  }

  build() {
    Column() {
      // 头部
      HeaderComponent({
        title: '健康管家',
        showAction: true,
        actionText: '王女士',
        onActionClick: () => {
          // 点击用户名
        }
      })

      // 滚动内容
      Scroll() {
        Column() {
          // 健康提醒
          this.buildHealthReminder()

          // 家庭成员健康
          this.buildFamilyHealth()

          // 体感操作
          this.buildBodySenseActions()

          // 今日健康任务
          this.buildTodayTasks()

          // 家庭健康概况
          this.buildHealthOverview()
        }
        .width('100%')
        .padding({ bottom: 20 })
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.BACKGROUND_COLOR)
  }

  @Builder
  buildHealthReminder() {
    if (this.healthReminders.length > 0) {
      CardComponent({
        title: '',
        showTitle: false,
        content: () => {
          Row() {
            Text('⚠️')
              .fontSize(20)
              .margin({ right: Constants.MARGIN_SMALL })

            Column() {
              Text(this.healthReminders[0].title)
                .fontSize(Constants.FONT_SIZE_MEDIUM)
                .fontWeight(FontWeight.Medium)
                .fontColor(Constants.TEXT_PRIMARY)
                .width('100%')
                .textAlign(TextAlign.Start)

              Text(this.healthReminders[0].content)
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .width('100%')
                .textAlign(TextAlign.Start)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start)

            Text('→')
              .fontSize(16)
              .fontColor(Constants.TEXT_SECONDARY)
          }
          .width('100%')
          .alignItems(VerticalAlign.Center)
          .padding(Constants.PADDING_MEDIUM)
          .backgroundColor('#FFF2E8')
          .borderRadius(Constants.BORDER_RADIUS)
        }
      })
    }
  }

  @Builder
  buildFamilyHealth() {
    CardComponent({
      title: '家庭成员健康',
      showAction: true,
      actionText: '查看全部',
      content: () => {
        Row() {
          ForEach(this.familyMembers, (member: FamilyMember, index: number) => {
            HealthMetricCard({ member: member })
              .margin({ right: index < this.familyMembers.length - 1 ? Constants.MARGIN_SMALL : 0 })
          })
        }
        .width('100%')
      },
      onActionClick: () => {
        // 查看全部家庭成员
      }
    })
  }

  @Builder
  buildBodySenseActions() {
    CardComponent({
      title: '体感操作',
      content: () => {
        Row() {
          ForEach(this.bodySenseActions, (action: BodySenseAction, index: number) => {
            Column() {
              Text('🔵')
                .fontSize(24)
                .width(48)
                .height(48)
                .textAlign(TextAlign.Center)
                .backgroundColor('#E6F7FF')
                .borderRadius(24)

              Text(action.title)
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_PRIMARY)
                .textAlign(TextAlign.Center)
                .margin({ top: Constants.MARGIN_SMALL })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)
            .onClick(() => {
              // 点击体感操作
            })
          })
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceEvenly)
      }
    })
  }

  @Builder
  buildTodayTasks() {
    CardComponent({
      title: '今日健康任务',
      showAction: true,
      actionText: '全部',
      content: () => {
        Column() {
          ForEach(this.healthTasks, (task: HealthTask, index: number) => {
            Column() {
              TaskItemComponent({
                task: task,
                onTaskToggle: (taskId: string, completed: boolean) => {
                  this.toggleTask(taskId, completed);
                }
              })

              if (index < this.healthTasks.length - 1) {
                Divider()
                  .color(Constants.BORDER_COLOR)
                  .margin({ top: Constants.MARGIN_SMALL, bottom: Constants.MARGIN_SMALL })
              }
            }
          })
        }
        .width('100%')
      },
      onActionClick: () => {
        // 查看全部任务
      }
    })
  }

  @Builder
  buildHealthOverview() {
    CardComponent({
      title: '家庭健康概况',
      showAction: true,
      actionText: '详情',
      content: () => {
        Column() {
          Row() {
            Text(this.healthStats.type)
              .fontSize(Constants.FONT_SIZE_MEDIUM)
              .fontColor(Constants.TEXT_PRIMARY)
              .layoutWeight(1)

            Text(`${this.healthStats.value}${this.healthStats.unit}`)
              .fontSize(Constants.FONT_SIZE_LARGE)
              .fontWeight(FontWeight.Bold)
              .fontColor(Constants.PRIMARY_COLOR)
          }
          .width('100%')
          .justifyContent(FlexAlign.SpaceBetween)
          .margin({ bottom: Constants.MARGIN_MEDIUM })

          ChartComponent({
            data: this.healthStats.data,
            labels: this.healthStats.labels,
            maxValue: 100,
            height: 100
          })
        }
        .width('100%')
      },
      onActionClick: () => {
        // 查看详情
      }
    })
  }

  private loadData() {
    this.familyMembers = DataUtils.getFamilyMembers();
    this.healthTasks = DataUtils.getTodayHealthTasks();
    this.bodySenseActions = DataUtils.getBodySenseActions();
    this.healthReminders = DataUtils.getHealthReminders();
    this.healthStats = DataUtils.getHealthStats();
  }

  private toggleTask(taskId: string, completed: boolean) {
    const taskIndex = this.healthTasks.findIndex(task => task.id === taskId);
    if (taskIndex !== -1) {
      this.healthTasks[taskIndex].completed = completed;
    }
  }
}
