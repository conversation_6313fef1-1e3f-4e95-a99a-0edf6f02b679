/**
 * 应用常量定义
 */
export class Constants {
    // 颜色常量
    static readonly PRIMARY_COLOR = '#1890FF';
    static readonly SECONDARY_COLOR = '#52C41A';
    static readonly WARNING_COLOR = '#FA8C16';
    static readonly ERROR_COLOR = '#F5222D';
    static readonly SUCCESS_COLOR = '#52C41A';
    static readonly TEXT_PRIMARY = '#262626';
    static readonly TEXT_SECONDARY = '#8C8C8C';
    static readonly BACKGROUND_COLOR = '#F5F5F5';
    static readonly WHITE = '#FFFFFF';
    static readonly BORDER_COLOR = '#E8E8E8';
    // 尺寸常量
    static readonly BORDER_RADIUS = 8;
    static readonly CARD_BORDER_RADIUS = 12;
    static readonly PADDING_SMALL = 8;
    static readonly PADDING_MEDIUM = 16;
    static readonly PADDING_LARGE = 24;
    static readonly MARGIN_SMALL = 8;
    static readonly MARGIN_MEDIUM = 16;
    static readonly MARGIN_LARGE = 24;
    // 字体大小
    static readonly FONT_SIZE_SMALL = 12;
    static readonly FONT_SIZE_MEDIUM = 14;
    static readonly FONT_SIZE_LARGE = 16;
    static readonly FONT_SIZE_TITLE = 18;
    static readonly FONT_SIZE_HEADER = 20;
    // 底部导航栏
    static readonly TAB_BAR_HEIGHT = 60;
    static readonly TAB_ICON_SIZE = 24;
    // 页面标识
    static readonly PAGE_HOME = 0;
    static readonly PAGE_ARCHIVE = 1;
    static readonly PAGE_CALENDAR = 2;
    static readonly PAGE_ASSISTANT = 3;
    static readonly PAGE_PROFILE = 4;
    // 动画时长
    static readonly ANIMATION_DURATION = 300;
}
