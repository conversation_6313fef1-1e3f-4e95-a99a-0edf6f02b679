import { Constants } from '../common/Constants';

/**
 * 简单图表组件
 */
@Component
export struct ChartComponent {
  @Prop data: number[] = [];
  @Prop labels: string[] = [];
  @Prop maxValue: number = 100;
  @Prop height: number = 120;
  @Prop showLabels: boolean = true;

  build() {
    Column() {
      // 图表区域
      Row() {
        ForEach(this.data, (value: number, index: number) => {
          Column() {
            // 柱状图
            Column()
              .width(20)
              .height((value / this.maxValue) * this.height)
              .backgroundColor(Constants.PRIMARY_COLOR)
              .borderRadius(2)
              .margin({ bottom: 4 })

            // 标签
            if (this.showLabels && this.labels[index]) {
              Text(this.labels[index])
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .textAlign(TextAlign.Center)
            }
          }
          .layoutWeight(1)
          .justifyContent(FlexAlign.End)
          .alignItems(HorizontalAlign.Center)
        })
      }
      .width('100%')
      .height(this.height + (this.showLabels ? 20 : 0))
      .alignItems(VerticalAlign.End)
      .justifyContent(FlexAlign.SpaceEvenly)
    }
    .width('100%')
  }
}
