import { Constants } from '../common/Constants';
import { HeaderComponent } from '../components/HeaderComponent';
import { CardComponent } from '../components/CardComponent';
import { CalendarComponent } from '../components/CalendarComponent';
import { TaskItemComponent } from '../components/TaskItemComponent';
import { ChartComponent } from '../components/ChartComponent';
import { DataUtils } from '../utils/DataUtils';
import { DateUtils } from '../utils/DateUtils';
import { 
  CalendarEvent, 
  HealthTask, 
  FamilyMember,
  HealthStats
} from '../models/HealthModels';

/**
 * 健康日历页面
 */
@Component
export struct CalendarPage {
  @State selectedDate: string = DateUtils.getToday();
  @State calendarEvents: CalendarEvent[] = [];
  @State todayTasks: HealthTask[] = [];
  @State familyMembers: FamilyMember[] = [];
  @State currentTabIndex: number = 0;

  private tabTitles = ['今日任务', '本月任务', '活动列表'];

  aboutToAppear() {
    this.loadData();
  }

  build() {
    Column() {
      // 头部
      HeaderComponent({
        title: '健康日历优化',
        showAction: true,
        actionIcon: '💙',
        onActionClick: () => {
          // 收藏功能
        }
      })

      // 滚动内容
      Scroll() {
        Column() {
          // 日历组件
          this.buildCalendar()

          // 标签页切换
          this.buildTabBar()

          // 标签页内容
          this.buildTabContent()

          // 健康活动追踪
          this.buildHealthTracking()

          // 家庭活动概览
          this.buildFamilyOverview()
        }
        .width('100%')
        .padding({ bottom: 20 })
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.BACKGROUND_COLOR)
  }

  @Builder
  buildCalendar() {
    CardComponent({
      title: '健康日历',
      showTitle: false,
      content: () => {
        CalendarComponent({
          onDateSelect: (date: string) => {
            this.selectedDate = date;
            this.loadTasksForDate(date);
          }
        })
      }
    })
  }

  @Builder
  buildTabBar() {
    Row() {
      ForEach(this.tabTitles, (title: string, index: number) => {
        Text(title)
          .fontSize(Constants.FONT_SIZE_MEDIUM)
          .fontColor(this.currentTabIndex === index ? Constants.PRIMARY_COLOR : Constants.TEXT_SECONDARY)
          .fontWeight(this.currentTabIndex === index ? FontWeight.Medium : FontWeight.Normal)
          .padding({
            top: Constants.PADDING_SMALL,
            bottom: Constants.PADDING_SMALL,
            left: Constants.PADDING_MEDIUM,
            right: Constants.PADDING_MEDIUM
          })
          .borderRadius(Constants.BORDER_RADIUS)
          .backgroundColor(this.currentTabIndex === index ? '#E6F7FF' : 'transparent')
          .onClick(() => {
            this.currentTabIndex = index;
          })
      })
    }
    .width('100%')
    .justifyContent(FlexAlign.SpaceEvenly)
    .padding(Constants.PADDING_MEDIUM)
    .backgroundColor(Constants.WHITE)
    .margin({ top: Constants.MARGIN_SMALL, bottom: Constants.MARGIN_SMALL })
    .borderRadius(Constants.CARD_BORDER_RADIUS)
  }

  @Builder
  buildTabContent() {
    if (this.currentTabIndex === 0) {
      this.buildTodayTasks()
    } else if (this.currentTabIndex === 1) {
      this.buildMonthTasks()
    } else {
      this.buildActivityList()
    }
  }

  @Builder
  buildTodayTasks() {
    CardComponent({
      title: `今日健康任务 (${DateUtils.formatDate(new Date(this.selectedDate), 'MM月DD日')})`,
      content: () => {
        if (this.todayTasks.length > 0) {
          Column() {
            ForEach(this.todayTasks, (task: HealthTask, index: number) => {
              Column() {
                TaskItemComponent({
                  task: task,
                  onTaskToggle: (taskId: string, completed: boolean) => {
                    this.toggleTask(taskId, completed);
                  }
                })

                if (index < this.todayTasks.length - 1) {
                  Divider()
                    .color(Constants.BORDER_COLOR)
                    .margin({ top: Constants.MARGIN_SMALL, bottom: Constants.MARGIN_SMALL })
                }
              }
            })
          }
          .width('100%')
        } else {
          Text('今日暂无健康任务')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.TEXT_SECONDARY)
            .textAlign(TextAlign.Center)
            .width('100%')
            .padding(Constants.PADDING_LARGE)
        }
      }
    })
  }

  @Builder
  buildMonthTasks() {
    CardComponent({
      title: '本月任务概览',
      content: () => {
        Column() {
          // 任务统计
          Row() {
            Column() {
              Text('已完成')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('15')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.SUCCESS_COLOR)
                .margin({ top: 4 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)

            Column() {
              Text('进行中')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('8')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.WARNING_COLOR)
                .margin({ top: 4 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)

            Column() {
              Text('未开始')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('3')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.TEXT_SECONDARY)
                .margin({ top: 4 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)
          }
          .width('100%')
          .padding(Constants.PADDING_MEDIUM)
          .backgroundColor('#F8F9FA')
          .borderRadius(Constants.BORDER_RADIUS)
        }
        .width('100%')
      }
    })
  }

  @Builder
  buildActivityList() {
    CardComponent({
      title: '每日运动',
      content: () => {
        Column() {
          Row() {
            Text('🏃')
              .fontSize(20)
              .margin({ right: Constants.MARGIN_SMALL })

            Column() {
              Text('每日运动')
                .fontSize(Constants.FONT_SIZE_MEDIUM)
                .fontWeight(FontWeight.Medium)
                .fontColor(Constants.TEXT_PRIMARY)
                .width('100%')
                .textAlign(TextAlign.Start)

              Text('全家人·每日一次·全天')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .width('100%')
                .textAlign(TextAlign.Start)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start)

            Text('✓')
              .fontSize(16)
              .fontColor(Constants.SUCCESS_COLOR)
              .width(24)
              .height(24)
              .textAlign(TextAlign.Center)
              .backgroundColor('#F6FFED')
              .borderRadius(12)
              .border({
                width: 1,
                color: Constants.SUCCESS_COLOR
              })
          }
          .width('100%')
          .alignItems(VerticalAlign.Center)

          Divider()
            .color(Constants.BORDER_COLOR)
            .margin({ top: Constants.MARGIN_MEDIUM, bottom: Constants.MARGIN_MEDIUM })

          Row() {
            Text('💊')
              .fontSize(20)
              .margin({ right: Constants.MARGIN_SMALL })

            Column() {
              Text('白内障')
                .fontSize(Constants.FONT_SIZE_MEDIUM)
                .fontWeight(FontWeight.Medium)
                .fontColor(Constants.TEXT_PRIMARY)
                .width('100%')
                .textAlign(TextAlign.Start)

              Text('全家人·每日一次·全天')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .width('100%')
                .textAlign(TextAlign.Start)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Start)

            Text('')
              .width(24)
              .height(24)
              .backgroundColor(Constants.WHITE)
              .borderRadius(12)
              .border({
                width: 1,
                color: Constants.BORDER_COLOR
              })
          }
          .width('100%')
          .alignItems(VerticalAlign.Center)
        }
        .width('100%')
      }
    })
  }

  @Builder
  buildHealthTracking() {
    CardComponent({
      title: '健康活动追踪',
      showAction: true,
      actionText: '全部',
      content: () => {
        Column() {
          Row() {
            Column() {
              Text('步数')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('7,845')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.PRIMARY_COLOR)
                .margin({ top: 4 })

              Text('目标: 10,000')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)

            Column() {
              Text('消耗')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)

              Text('320')
                .fontSize(Constants.FONT_SIZE_LARGE)
                .fontWeight(FontWeight.Bold)
                .fontColor(Constants.SUCCESS_COLOR)
                .margin({ top: 4 })

              Text('千卡')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)
          }
          .width('100%')

          // 活动时间分布图表
          Text('活动时间分布')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.TEXT_PRIMARY)
            .width('100%')
            .textAlign(TextAlign.Start)
            .margin({ top: Constants.MARGIN_LARGE, bottom: Constants.MARGIN_SMALL })

          ChartComponent({
            data: [3, 5, 7, 6, 8, 9, 7],
            labels: ['7/30', '7/31', '8/1', '8/2', '8/3', '8/4', '8/5'],
            maxValue: 10,
            height: 80
          })
        }
        .width('100%')
      },
      onActionClick: () => {
        // 查看全部追踪数据
      }
    })
  }

  @Builder
  buildFamilyOverview() {
    CardComponent({
      title: '家庭活动概览',
      showAction: true,
      actionText: '全部',
      content: () => {
        Row() {
          ForEach(this.familyMembers, (member: FamilyMember, index: number) => {
            Column() {
              // 头像
              Text('👤')
                .fontSize(20)
                .width(36)
                .height(36)
                .textAlign(TextAlign.Center)
                .backgroundColor('#F0F0F0')
                .borderRadius(18)

              Text(member.name)
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_PRIMARY)
                .margin({ top: 4 })

              Text('4,320步')
                .fontSize(Constants.FONT_SIZE_SMALL)
                .fontColor(Constants.TEXT_SECONDARY)
                .margin({ top: 2 })
            }
            .layoutWeight(1)
            .alignItems(HorizontalAlign.Center)
          })
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceEvenly)
      },
      onActionClick: () => {
        // 查看家庭详情
      }
    })
  }

  private loadData() {
    this.calendarEvents = DataUtils.getCalendarEvents();
    this.todayTasks = DataUtils.getTodayHealthTasks();
    this.familyMembers = DataUtils.getFamilyMembers();
  }

  private loadTasksForDate(date: string) {
    // 根据选择的日期加载对应的任务
    this.todayTasks = DataUtils.getTodayHealthTasks();
  }

  private toggleTask(taskId: string, completed: boolean) {
    const taskIndex = this.todayTasks.findIndex(task => task.id === taskId);
    if (taskIndex !== -1) {
      this.todayTasks[taskIndex].completed = completed;
    }
  }
}
