import { Constants } from '../common/Constants';
import { Styles } from '../common/Styles';
import { HomePage } from './HomePage';
import { ArchivePage } from './ArchivePage';
import { CalendarPage } from './CalendarPage';
import { AssistantPage } from './AssistantPage';
import { ProfilePage } from './ProfilePage';

@Entry
@Component
struct Index {
  @State currentPageIndex: number = 0;

  // 底部导航栏数据
  private tabBarData = [
    { text: '首页', icon: '', selectedIcon: '', page: 0 },
    { text: '档案', icon: '', selectedIcon: '', page: 1 },
    { text: '日历', icon: '', selectedIcon: '', page: 2 },
    { text: '助手', icon: '', selectedIcon: '', page: 3 },
    { text: '我的', icon: '', selectedIcon: '', page: 4 }
  ];

  build() {
    Column() {
      // 主内容区域
      Stack() {
        this.buildPageContent()
      }
      .layoutWeight(1)

      // 底部导航栏
      this.buildTabBar()
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.BACKGROUND_COLOR)
  }

  @Builder
  buildPageContent() {
    if (this.currentPageIndex === 0) {
      this.buildHomePage()
    } else if (this.currentPageIndex === 1) {
      this.buildArchivePage()
    } else if (this.currentPageIndex === 2) {
      this.buildCalendarPage()
    } else if (this.currentPageIndex === 3) {
      this.buildAssistantPage()
    } else if (this.currentPageIndex === 4) {
      this.buildProfilePage()
    }
  }

  @Builder
  buildTabBar() {
    Row() {
      ForEach(this.tabBarData, (item, index) => {
        Column() {
          // 图标占位
          Text('📱')
            .fontSize(Constants.TAB_ICON_SIZE)
            .fontColor(this.currentPageIndex === index ? Constants.PRIMARY_COLOR : Constants.TEXT_SECONDARY)

          Text(item.text)
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(this.currentPageIndex === index ? Constants.PRIMARY_COLOR : Constants.TEXT_SECONDARY)
            .margin({ top: 4 })
        }
        .layoutWeight(1)
        .justifyContent(FlexAlign.Center)
        .alignItems(HorizontalAlign.Center)
        .onClick(() => {
          this.currentPageIndex = index;
        })
      })
    }
    .width('100%')
    .height(Constants.TAB_BAR_HEIGHT)
    .backgroundColor(Constants.WHITE)
    .border({
      width: { top: 1 },
      color: Constants.BORDER_COLOR
    })
  }

  @Builder
  buildHomePage() {
    HomePage()
  }

  @Builder
  buildArchivePage() {
    ArchivePage()
  }

  @Builder
  buildCalendarPage() {
    CalendarPage()
  }

  @Builder
  buildAssistantPage() {
    AssistantPage()
  }

  @Builder
  buildProfilePage() {
    ProfilePage()
  }
}