import { Constants } from '../common/Constants';

/**
 * 通用头部组件
 */
@Component
export struct HeaderComponent {
  @Prop title: string = '';
  @Prop showBack: boolean = false;
  @Prop showAction: boolean = false;
  @Prop actionText: string = '';
  @Prop actionIcon: string = '';
  onBackClick?: () => void;
  onActionClick?: () => void;

  build() {
    Row() {
      // 左侧区域
      Row() {
        if (this.showBack) {
          Text('←')
            .fontSize(20)
            .fontColor(Constants.TEXT_PRIMARY)
            .onClick(() => {
              if (this.onBackClick) {
                this.onBackClick();
              }
            })
        }
      }
      .width(60)
      .justifyContent(FlexAlign.Start)

      // 中间标题
      Text(this.title)
        .fontSize(Constants.FONT_SIZE_HEADER)
        .fontWeight(FontWeight.Medium)
        .fontColor(Constants.TEXT_PRIMARY)
        .layoutWeight(1)
        .textAlign(TextAlign.Center)

      // 右侧区域
      Row() {
        if (this.showAction) {
          if (this.actionText) {
            Text(this.actionText)
              .fontSize(Constants.FONT_SIZE_MEDIUM)
              .fontColor(Constants.PRIMARY_COLOR)
              .onClick(() => {
                if (this.onActionClick) {
                  this.onActionClick();
                }
              })
          } else if (this.actionIcon) {
            Text(this.actionIcon)
              .fontSize(20)
              .fontColor(Constants.TEXT_PRIMARY)
              .onClick(() => {
                if (this.onActionClick) {
                  this.onActionClick();
                }
              })
          }
        }
      }
      .width(60)
      .justifyContent(FlexAlign.End)
    }
    .width('100%')
    .height(56)
    .padding({
      left: Constants.PADDING_MEDIUM,
      right: Constants.PADDING_MEDIUM
    })
    .backgroundColor(Constants.WHITE)
    .justifyContent(FlexAlign.SpaceBetween)
    .alignItems(VerticalAlign.Center)
  }
}
