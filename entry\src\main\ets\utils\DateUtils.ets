/**
 * 日期工具类
 */
export class DateUtils {
  /**
   * 格式化日期
   * @param date 日期对象
   * @param format 格式字符串，如 'YYYY-MM-DD'
   * @returns 格式化后的日期字符串
   */
  static formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    const minute = String(date.getMinutes()).padStart(2, '0');
    const second = String(date.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second);
  }

  /**
   * 获取今天的日期字符串
   * @returns 今天的日期字符串
   */
  static getToday(): string {
    return this.formatDate(new Date(), 'YYYY-MM-DD');
  }

  /**
   * 获取当前时间字符串
   * @returns 当前时间字符串
   */
  static getCurrentTime(): string {
    return this.formatDate(new Date(), 'HH:mm');
  }

  /**
   * 获取相对时间描述
   * @param date 日期字符串或日期对象
   * @returns 相对时间描述，如"刚刚"、"5分钟前"等
   */
  static getRelativeTime(date: string | Date): string {
    const targetDate = typeof date === 'string' ? new Date(date) : date;
    const now = new Date();
    const diff = now.getTime() - targetDate.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return this.formatDate(targetDate, 'MM-DD');
    }
  }

  /**
   * 获取月份的天数
   * @param year 年份
   * @param month 月份（1-12）
   * @returns 该月的天数
   */
  static getDaysInMonth(year: number, month: number): number {
    return new Date(year, month, 0).getDate();
  }

  /**
   * 获取月份的第一天是星期几
   * @param year 年份
   * @param month 月份（1-12）
   * @returns 星期几（0-6，0表示星期日）
   */
  static getFirstDayOfMonth(year: number, month: number): number {
    return new Date(year, month - 1, 1).getDay();
  }

  /**
   * 判断是否为今天
   * @param date 日期字符串或日期对象
   * @returns 是否为今天
   */
  static isToday(date: string | Date): boolean {
    const targetDate = typeof date === 'string' ? new Date(date) : date;
    const today = new Date();
    return targetDate.toDateString() === today.toDateString();
  }

  /**
   * 获取星期几的中文名称
   * @param dayOfWeek 星期几（0-6）
   * @returns 中文星期名称
   */
  static getWeekdayName(dayOfWeek: number): string {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    return weekdays[dayOfWeek];
  }

  /**
   * 获取月份的中文名称
   * @param month 月份（1-12）
   * @returns 中文月份名称
   */
  static getMonthName(month: number): string {
    return `${month}月`;
  }
}
