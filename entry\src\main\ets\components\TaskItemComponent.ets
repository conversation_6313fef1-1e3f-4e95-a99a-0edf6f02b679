import { Constants } from '../common/Constants';
import { HealthTask } from '../models/HealthModels';

/**
 * 任务项组件
 */
@Component
export struct TaskItemComponent {
  @Prop task: HealthTask;
  onTaskToggle?: (taskId: string, completed: boolean) => void;

  build() {
    Row() {
      // 任务图标
      Text('💊')
        .fontSize(24)
        .width(40)
        .height(40)
        .textAlign(TextAlign.Center)
        .backgroundColor('#E6F7FF')
        .borderRadius(20)

      // 任务信息
      Column() {
        Text(this.task.title)
          .fontSize(Constants.FONT_SIZE_MEDIUM)
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.TEXT_PRIMARY)
          .width('100%')
          .textAlign(TextAlign.Start)

        Text(this.task.description)
          .fontSize(Constants.FONT_SIZE_SMALL)
          .fontColor(Constants.TEXT_SECONDARY)
          .width('100%')
          .textAlign(TextAlign.Start)
          .margin({ top: 2 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: Constants.MARGIN_SMALL })

      // 完成状态
      Row() {
        if (this.task.completed) {
          Text('✓')
            .fontSize(16)
            .fontColor(Constants.SUCCESS_COLOR)
            .width(24)
            .height(24)
            .textAlign(TextAlign.Center)
            .backgroundColor('#F6FFED')
            .borderRadius(12)
            .border({
              width: 1,
              color: Constants.SUCCESS_COLOR
            })
        } else {
          Text('')
            .width(24)
            .height(24)
            .backgroundColor(Constants.WHITE)
            .borderRadius(12)
            .border({
              width: 1,
              color: Constants.BORDER_COLOR
            })
        }
      }
      .onClick(() => {
        if (this.onTaskToggle) {
          this.onTaskToggle(this.task.id, !this.task.completed);
        }
      })
    }
    .width('100%')
    .padding(Constants.PADDING_MEDIUM)
    .alignItems(VerticalAlign.Center)
  }
}
