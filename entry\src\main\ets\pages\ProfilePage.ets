import { Constants } from '../common/Constants';
import { HeaderComponent } from '../components/HeaderComponent';
import { CardComponent } from '../components/CardComponent';
import { DataUtils } from '../utils/DataUtils';
import { UserInfo, SettingItem } from '../models/HealthModels';

/**
 * 我的页面
 */
@Component
export struct ProfilePage {
  @State currentUser: UserInfo = {} as UserInfo;
  @State settingItems: SettingItem[] = [];

  aboutToAppear() {
    this.loadData();
  }

  build() {
    Column() {
      // 头部
      HeaderComponent({
        title: '我的主页面',
        showAction: false
      })

      // 滚动内容
      Scroll() {
        Column() {
          // 用户信息卡片
          this.buildUserProfile()

          // 账户与服务
          this.buildAccountServices()

          // 设置与帮助
          this.buildSettingsAndHelp()

          // 退出登录按钮
          this.buildLogoutButton()
        }
        .width('100%')
        .padding({ bottom: 20 })
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.BACKGROUND_COLOR)
  }

  @Builder
  buildUserProfile() {
    CardComponent({
      title: '',
      showTitle: false,
      content: () => {
        Row() {
          // 用户头像
          Text('👤')
            .fontSize(32)
            .width(64)
            .height(64)
            .textAlign(TextAlign.Center)
            .backgroundColor('#E6F7FF')
            .borderRadius(32)

          // 用户信息
          Column() {
            Text(this.currentUser.name)
              .fontSize(Constants.FONT_SIZE_HEADER)
              .fontWeight(FontWeight.Bold)
              .fontColor(Constants.TEXT_PRIMARY)
              .width('100%')
              .textAlign(TextAlign.Start)

            Text(`ID: ${this.currentUser.phone}`)
              .fontSize(Constants.FONT_SIZE_MEDIUM)
              .fontColor(Constants.TEXT_SECONDARY)
              .width('100%')
              .textAlign(TextAlign.Start)
              .margin({ top: 4 })
          }
          .layoutWeight(1)
          .alignItems(HorizontalAlign.Start)
          .margin({ left: Constants.MARGIN_MEDIUM })

          // 编辑按钮
          Text('编辑')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.PRIMARY_COLOR)
            .padding({
              top: 6,
              bottom: 6,
              left: 12,
              right: 12
            })
            .backgroundColor('#E6F7FF')
            .borderRadius(Constants.BORDER_RADIUS)
            .onClick(() => {
              // 编辑用户信息
            })
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
      }
    })
  }

  @Builder
  buildAccountServices() {
    CardComponent({
      title: '账户与服务',
      content: () => {
        Column() {
          this.buildServiceItem('我的家庭', '管理家庭成员信息', '👨‍👩‍👧‍👦')
          this.buildDivider()
          this.buildServiceItem('消息通知', '各类系统通知设置', '🔔')
          this.buildDivider()
          this.buildServiceItem('我的健康报告', '查看与管理个人健康报告', '📊')
          this.buildDivider()
          this.buildServiceItem('我的收藏', '查看已收藏的健康内容', '❤️')
        }
        .width('100%')
      }
    })
  }

  @Builder
  buildSettingsAndHelp() {
    CardComponent({
      title: '设置与帮助',
      content: () => {
        Column() {
          this.buildServiceItem('设置', '通用设置、隐私与安全', '⚙️')
          this.buildDivider()
          this.buildServiceItem('帮助与反馈', '常见问题、联系客服', '❓')
        }
        .width('100%')
      }
    })
  }

  @Builder
  buildServiceItem(title: string, subtitle: string, icon: string) {
    Row() {
      // 图标
      Text(icon)
        .fontSize(24)
        .width(40)
        .height(40)
        .textAlign(TextAlign.Center)
        .backgroundColor('#F8F9FA')
        .borderRadius(20)

      // 文本信息
      Column() {
        Text(title)
          .fontSize(Constants.FONT_SIZE_MEDIUM)
          .fontWeight(FontWeight.Medium)
          .fontColor(Constants.TEXT_PRIMARY)
          .width('100%')
          .textAlign(TextAlign.Start)

        Text(subtitle)
          .fontSize(Constants.FONT_SIZE_SMALL)
          .fontColor(Constants.TEXT_SECONDARY)
          .width('100%')
          .textAlign(TextAlign.Start)
          .margin({ top: 2 })
      }
      .layoutWeight(1)
      .alignItems(HorizontalAlign.Start)
      .margin({ left: Constants.MARGIN_MEDIUM })

      // 箭头
      Text('→')
        .fontSize(16)
        .fontColor(Constants.TEXT_SECONDARY)
    }
    .width('100%')
    .alignItems(VerticalAlign.Center)
    .padding({
      top: Constants.PADDING_MEDIUM,
      bottom: Constants.PADDING_MEDIUM
    })
    .onClick(() => {
      this.handleServiceItemClick(title);
    })
  }

  @Builder
  buildDivider() {
    Divider()
      .color(Constants.BORDER_COLOR)
      .margin({ left: 56 })
  }

  @Builder
  buildLogoutButton() {
    Button('退出登录')
      .fontSize(Constants.FONT_SIZE_MEDIUM)
      .fontColor(Constants.ERROR_COLOR)
      .backgroundColor(Constants.WHITE)
      .border({
        width: 1,
        color: Constants.ERROR_COLOR
      })
      .borderRadius(Constants.BORDER_RADIUS)
      .width('100%')
      .height(48)
      .margin({ top: Constants.MARGIN_LARGE })
      .onClick(() => {
        this.handleLogout();
      })
  }

  private loadData() {
    this.currentUser = DataUtils.getCurrentUser();
    this.settingItems = DataUtils.getSettingItems();
  }

  private handleServiceItemClick(title: string) {
    // 处理服务项点击事件
    console.log(`点击了: ${title}`);
  }

  private handleLogout() {
    // 处理退出登录
    console.log('退出登录');
  }
}
