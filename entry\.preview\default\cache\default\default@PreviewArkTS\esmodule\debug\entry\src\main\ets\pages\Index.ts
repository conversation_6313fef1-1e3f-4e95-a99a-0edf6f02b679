if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface Index_Params {
    currentPageIndex?: number;
    tabBarData?;
}
import { Constants } from "@normalized:N&&&entry/src/main/ets/common/Constants&";
class Index extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__currentPageIndex = new ObservedPropertySimplePU(0, this, "currentPageIndex");
        this.tabBarData = [
            { text: '首页', icon: '', selectedIcon: '', page: 0 },
            { text: '档案', icon: '', selectedIcon: '', page: 1 },
            { text: '日历', icon: '', selectedIcon: '', page: 2 },
            { text: '助手', icon: '', selectedIcon: '', page: 3 },
            { text: '我的', icon: '', selectedIcon: '', page: 4 }
        ];
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: Index_Params) {
        if (params.currentPageIndex !== undefined) {
            this.currentPageIndex = params.currentPageIndex;
        }
        if (params.tabBarData !== undefined) {
            this.tabBarData = params.tabBarData;
        }
    }
    updateStateVars(params: Index_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__currentPageIndex.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__currentPageIndex.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __currentPageIndex: ObservedPropertySimplePU<number>;
    get currentPageIndex() {
        return this.__currentPageIndex.get();
    }
    set currentPageIndex(newValue: number) {
        this.__currentPageIndex.set(newValue);
    }
    // 底部导航栏数据
    private tabBarData;
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.debugLine("entry/src/main/ets/pages/Index.ets(19:5)", "entry");
            Column.width('100%');
            Column.height('100%');
            Column.backgroundColor(Constants.BACKGROUND_COLOR);
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // 主内容区域
            Stack.create();
            Stack.debugLine("entry/src/main/ets/pages/Index.ets(21:7)", "entry");
            // 主内容区域
            Stack.layoutWeight(1);
        }, Stack);
        this.buildPageContent.bind(this)();
        // 主内容区域
        Stack.pop();
        // 底部导航栏
        this.buildTabBar.bind(this)();
        Column.pop();
    }
    buildPageContent(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.currentPageIndex === 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.buildHomePage.bind(this)();
                });
            }
            else if (this.currentPageIndex === 1) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildArchivePage.bind(this)();
                });
            }
            else if (this.currentPageIndex === 2) {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.buildCalendarPage.bind(this)();
                });
            }
            else if (this.currentPageIndex === 3) {
                this.ifElseBranchUpdateFunction(3, () => {
                    this.buildAssistantPage.bind(this)();
                });
            }
            else if (this.currentPageIndex === 4) {
                this.ifElseBranchUpdateFunction(4, () => {
                    this.buildProfilePage.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(5, () => {
                });
            }
        }, If);
        If.pop();
    }
    buildTabBar(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.debugLine("entry/src/main/ets/pages/Index.ets(51:5)", "entry");
            Row.width('100%');
            Row.height(Constants.TAB_BAR_HEIGHT);
            Row.backgroundColor(Constants.WHITE);
            Row.border({
                width: { top: 1 },
                color: Constants.BORDER_COLOR
            });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index) => {
                const item = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Column.create();
                    Column.debugLine("entry/src/main/ets/pages/Index.ets(53:9)", "entry");
                    Column.layoutWeight(1);
                    Column.justifyContent(FlexAlign.Center);
                    Column.alignItems(HorizontalAlign.Center);
                    Column.onClick(() => {
                        this.currentPageIndex = index;
                    });
                }, Column);
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    // 图标占位
                    Text.create('📱');
                    Text.debugLine("entry/src/main/ets/pages/Index.ets(55:11)", "entry");
                    // 图标占位
                    Text.fontSize(Constants.TAB_ICON_SIZE);
                    // 图标占位
                    Text.fontColor(this.currentPageIndex === index ? Constants.PRIMARY_COLOR : Constants.TEXT_SECONDARY);
                }, Text);
                // 图标占位
                Text.pop();
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    Text.create(item.text);
                    Text.debugLine("entry/src/main/ets/pages/Index.ets(59:11)", "entry");
                    Text.fontSize(Constants.FONT_SIZE_SMALL);
                    Text.fontColor(this.currentPageIndex === index ? Constants.PRIMARY_COLOR : Constants.TEXT_SECONDARY);
                    Text.margin({ top: 4 });
                }, Text);
                Text.pop();
                Column.pop();
            };
            this.forEachUpdateFunction(elmtId, this.tabBarData, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Row.pop();
    }
    buildHomePage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('首页内容');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(83:5)", "entry");
            Text.fontSize(Constants.FONT_SIZE_LARGE);
            Text.fontColor(Constants.TEXT_PRIMARY);
        }, Text);
        Text.pop();
    }
    buildArchivePage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('档案页面');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(90:5)", "entry");
            Text.fontSize(Constants.FONT_SIZE_LARGE);
            Text.fontColor(Constants.TEXT_PRIMARY);
        }, Text);
        Text.pop();
    }
    buildCalendarPage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('日历页面');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(97:5)", "entry");
            Text.fontSize(Constants.FONT_SIZE_LARGE);
            Text.fontColor(Constants.TEXT_PRIMARY);
        }, Text);
        Text.pop();
    }
    buildAssistantPage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('智能助手');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(104:5)", "entry");
            Text.fontSize(Constants.FONT_SIZE_LARGE);
            Text.fontColor(Constants.TEXT_PRIMARY);
        }, Text);
        Text.pop();
    }
    buildProfilePage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('我的页面');
            Text.debugLine("entry/src/main/ets/pages/Index.ets(111:5)", "entry");
            Text.fontSize(Constants.FONT_SIZE_LARGE);
            Text.fontColor(Constants.TEXT_PRIMARY);
        }, Text);
        Text.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName(): string {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.example.mobile", moduleName: "entry", pagePath: "pages/Index", pageFullPath: "entry/src/main/ets/pages/Index", integratedHsp: "false", moduleType: "followWithHap" });
