import { Constants } from '../common/Constants';
import { FamilyMember } from '../models/HealthModels';

/**
 * 健康指标卡片组件
 */
@Component
export struct HealthMetricCard {
  @Prop member: FamilyMember;

  build() {
    Column() {
      // 成员头像和姓名
      Row() {
        // 头像占位
        Text('👤')
          .fontSize(24)
          .width(40)
          .height(40)
          .textAlign(TextAlign.Center)
          .backgroundColor('#F0F0F0')
          .borderRadius(20)

        Column() {
          Text(this.member.name)
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.TEXT_PRIMARY)

          Text(`年龄 ${this.member.age}岁`)
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_SECONDARY)
            .margin({ top: 2 })
        }
        .alignItems(HorizontalAlign.Start)
        .margin({ left: Constants.MARGIN_SMALL })
        .layoutWeight(1)
      }
      .width('100%')
      .alignItems(VerticalAlign.Center)

      // 健康指标
      Column() {
        Row() {
          Text('血压')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_SECONDARY)

          Text('120/80 mmHg')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .margin({ top: Constants.MARGIN_SMALL })

        Row() {
          Text('心率')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_SECONDARY)

          Text('118/75 mmHg')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .margin({ top: 4 })

        Row() {
          Text('今日步数')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_SECONDARY)

          Text('6,530 步')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontWeight(FontWeight.Medium)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .margin({ top: 4 })

        Row() {
          Text('今日任务')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_SECONDARY)

          Row() {
            Text('2/3')
              .fontSize(Constants.FONT_SIZE_MEDIUM)
              .fontWeight(FontWeight.Medium)
              .fontColor(Constants.TEXT_PRIMARY)

            Text('⭐')
              .fontSize(16)
              .margin({ left: 4 })
          }
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .margin({ top: 4 })
      }
      .width('100%')
    }
    .width('100%')
    .padding(Constants.PADDING_MEDIUM)
    .backgroundColor(Constants.WHITE)
    .borderRadius(Constants.CARD_BORDER_RADIUS)
    .shadow({
      radius: 4,
      color: '#00000010',
      offsetX: 0,
      offsetY: 2
    })
  }
}
