import { Constants } from '../common/Constants';
import { HeaderComponent } from '../components/HeaderComponent';
import { CardComponent } from '../components/CardComponent';
import { ChartComponent } from '../components/ChartComponent';
import { DateUtils } from '../utils/DateUtils';
import { AssistantMessage } from '../models/HealthModels';

/**
 * 智能助手页面
 */
@Component
export struct AssistantPage {
  @State messages: AssistantMessage[] = [];
  @State inputText: string = '';
  @State isLoading: boolean = false;

  aboutToAppear() {
    this.initializeChat();
  }

  build() {
    Column() {
      // 头部
      HeaderComponent({
        title: '智能助手优化',
        showAction: true,
        actionIcon: '↻',
        onActionClick: () => {
          this.resetChat();
        }
      })

      // 聊天内容区域
      Scroll() {
        Column() {
          ForEach(this.messages, (message: AssistantMessage, index: number) => {
            this.buildMessageItem(message, index)
          })

          // 加载指示器
          if (this.isLoading) {
            this.buildLoadingIndicator()
          }
        }
        .width('100%')
        .padding({ top: Constants.PADDING_MEDIUM, bottom: 80 })
      }
      .layoutWeight(1)
      .scrollBar(BarState.Off)

      // 底部输入区域
      this.buildInputArea()
    }
    .width('100%')
    .height('100%')
    .backgroundColor(Constants.BACKGROUND_COLOR)
  }

  @Builder
  buildMessageItem(message: AssistantMessage, index: number) {
    Row() {
      if (message.type === 'user') {
        // 用户消息 - 右对齐
        Blank()
          .layoutWeight(1)

        Column() {
          Text(message.content)
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.WHITE)
            .padding(Constants.PADDING_MEDIUM)
            .backgroundColor(Constants.PRIMARY_COLOR)
            .borderRadius(Constants.BORDER_RADIUS)
            .maxLines(10)
        }
        .alignItems(HorizontalAlign.End)
        .margin({ left: 60 })

        Text('👤')
          .fontSize(20)
          .width(36)
          .height(36)
          .textAlign(TextAlign.Center)
          .backgroundColor('#E6F7FF')
          .borderRadius(18)
          .margin({ left: Constants.MARGIN_SMALL })
      } else {
        // 助手消息 - 左对齐
        Text('🤖')
          .fontSize(20)
          .width(36)
          .height(36)
          .textAlign(TextAlign.Center)
          .backgroundColor('#F0F0F0')
          .borderRadius(18)
          .margin({ right: Constants.MARGIN_SMALL })

        Column() {
          Text(message.content)
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.TEXT_PRIMARY)
            .padding(Constants.PADDING_MEDIUM)
            .backgroundColor(Constants.WHITE)
            .borderRadius(Constants.BORDER_RADIUS)
            .maxLines(20)

          // 如果包含图表数据
          if (message.isChart && message.chartData) {
            this.buildChartMessage()
          }
        }
        .alignItems(HorizontalAlign.Start)
        .margin({ right: 60 })

        Blank()
          .layoutWeight(1)
      }
    }
    .width('100%')
    .alignItems(VerticalAlign.Top)
    .margin({ bottom: Constants.MARGIN_MEDIUM })
    .padding({ left: Constants.PADDING_MEDIUM, right: Constants.PADDING_MEDIUM })
  }

  @Builder
  buildChartMessage() {
    Column() {
      Text('根据您的健康数据，我为您生成了一份血压趋势分析报告：')
        .fontSize(Constants.FONT_SIZE_MEDIUM)
        .fontColor(Constants.TEXT_PRIMARY)
        .width('100%')
        .textAlign(TextAlign.Start)
        .margin({ bottom: Constants.MARGIN_MEDIUM })

      // 血压趋势图
      ChartComponent({
        data: [10, 8, 6, 4, 2, 4, 6],
        labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
        maxValue: 12,
        height: 100
      })

      Text('您的血压趋势可以看到2天以内，总体趋势良好。保持目前的和饮食习惯，定期监测血压变化。')
        .fontSize(Constants.FONT_SIZE_MEDIUM)
        .fontColor(Constants.TEXT_PRIMARY)
        .width('100%')
        .textAlign(TextAlign.Start)
        .margin({ top: Constants.MARGIN_MEDIUM })

      // 健康分析报告卡片
      this.buildHealthReportCard()

      // 体感咨询按钮
      this.buildConsultationButtons()
    }
    .width('100%')
    .padding(Constants.PADDING_MEDIUM)
    .backgroundColor('#F8F9FA')
    .borderRadius(Constants.BORDER_RADIUS)
    .margin({ top: Constants.MARGIN_MEDIUM })
  }

  @Builder
  buildHealthReportCard() {
    Column() {
      Row() {
        Text('健康分析报告')
          .fontSize(Constants.FONT_SIZE_TITLE)
          .fontWeight(FontWeight.Bold)
          .fontColor(Constants.TEXT_PRIMARY)
          .layoutWeight(1)

        Text('→')
          .fontSize(16)
          .fontColor(Constants.PRIMARY_COLOR)
      }
      .width('100%')
      .justifyContent(FlexAlign.SpaceBetween)
      .alignItems(VerticalAlign.Center)
      .margin({ bottom: Constants.MARGIN_MEDIUM })

      // 健康指标
      Column() {
        Row() {
          Text('🟢')
            .fontSize(12)
            .margin({ right: 8 })

          Text('健康状况良好：78分（良好）')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)

        Row() {
          Text('🟡')
            .fontSize(12)
            .margin({ right: 8 })

          Text('今日健康指数高于平均水平')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ top: 4 })

        Row() {
          Text('🔴')
            .fontSize(12)
            .margin({ right: 8 })

          Text('睡眠质量不足可能影响血压稳定性')
            .fontSize(Constants.FONT_SIZE_MEDIUM)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .width('100%')
        .alignItems(VerticalAlign.Center)
        .margin({ top: 4 })
      }
      .width('100%')

      Text('建议：保持规律的睡眠时间，避免熬夜，有助于血压稳定。如有疑问，请咨询专业医生。')
        .fontSize(Constants.FONT_SIZE_SMALL)
        .fontColor(Constants.TEXT_SECONDARY)
        .width('100%')
        .textAlign(TextAlign.Start)
        .margin({ top: Constants.MARGIN_MEDIUM })
    }
    .width('100%')
    .padding(Constants.PADDING_MEDIUM)
    .backgroundColor(Constants.WHITE)
    .borderRadius(Constants.BORDER_RADIUS)
    .margin({ top: Constants.MARGIN_MEDIUM })
  }

  @Builder
  buildConsultationButtons() {
    Column() {
      Text('体感咨询')
        .fontSize(Constants.FONT_SIZE_TITLE)
        .fontWeight(FontWeight.Medium)
        .fontColor(Constants.TEXT_PRIMARY)
        .width('100%')
        .textAlign(TextAlign.Start)
        .margin({ bottom: Constants.MARGIN_MEDIUM })

      Row() {
        Column() {
          Text('🩺')
            .fontSize(20)
            .margin({ bottom: 4 })

          Text('健康咨询')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)
        .padding(Constants.PADDING_MEDIUM)
        .backgroundColor('#E6F7FF')
        .borderRadius(Constants.BORDER_RADIUS)
        .onClick(() => {
          this.sendMessage('我想进行健康咨询');
        })

        Column() {
          Text('💊')
            .fontSize(20)
            .margin({ bottom: 4 })

          Text('用药咨询')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)
        .padding(Constants.PADDING_MEDIUM)
        .backgroundColor('#F6FFED')
        .borderRadius(Constants.BORDER_RADIUS)
        .margin({ left: Constants.MARGIN_SMALL })
        .onClick(() => {
          this.sendMessage('我想咨询用药问题');
        })

        Column() {
          Text('🏃')
            .fontSize(20)
            .margin({ bottom: 4 })

          Text('运动建议')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)
        .padding(Constants.PADDING_MEDIUM)
        .backgroundColor('#FFF2E8')
        .borderRadius(Constants.BORDER_RADIUS)
        .margin({ left: Constants.MARGIN_SMALL })
        .onClick(() => {
          this.sendMessage('请给我一些运动建议');
        })

        Column() {
          Text('🍎')
            .fontSize(20)
            .margin({ bottom: 4 })

          Text('饮食建议')
            .fontSize(Constants.FONT_SIZE_SMALL)
            .fontColor(Constants.TEXT_PRIMARY)
        }
        .layoutWeight(1)
        .alignItems(HorizontalAlign.Center)
        .padding(Constants.PADDING_MEDIUM)
        .backgroundColor('#F9F0FF')
        .borderRadius(Constants.BORDER_RADIUS)
        .margin({ left: Constants.MARGIN_SMALL })
        .onClick(() => {
          this.sendMessage('请给我一些饮食建议');
        })
      }
      .width('100%')
    }
    .width('100%')
    .margin({ top: Constants.MARGIN_LARGE })
  }

  @Builder
  buildLoadingIndicator() {
    Row() {
      Text('🤖')
        .fontSize(20)
        .width(36)
        .height(36)
        .textAlign(TextAlign.Center)
        .backgroundColor('#F0F0F0')
        .borderRadius(18)
        .margin({ right: Constants.MARGIN_SMALL })

      Text('正在思考中...')
        .fontSize(Constants.FONT_SIZE_MEDIUM)
        .fontColor(Constants.TEXT_SECONDARY)
        .padding(Constants.PADDING_MEDIUM)
        .backgroundColor(Constants.WHITE)
        .borderRadius(Constants.BORDER_RADIUS)

      Blank()
        .layoutWeight(1)
    }
    .width('100%')
    .alignItems(VerticalAlign.Center)
    .margin({ bottom: Constants.MARGIN_MEDIUM })
    .padding({ left: Constants.PADDING_MEDIUM, right: Constants.PADDING_MEDIUM })
  }

  @Builder
  buildInputArea() {
    Row() {
      TextInput({ placeholder: '输入您的问题' })
        .layoutWeight(1)
        .fontSize(Constants.FONT_SIZE_MEDIUM)
        .backgroundColor(Constants.WHITE)
        .borderRadius(Constants.BORDER_RADIUS)
        .padding(Constants.PADDING_MEDIUM)
        .onChange((value: string) => {
          this.inputText = value;
        })

      Button('发送')
        .fontSize(Constants.FONT_SIZE_MEDIUM)
        .fontColor(Constants.WHITE)
        .backgroundColor(Constants.PRIMARY_COLOR)
        .borderRadius(Constants.BORDER_RADIUS)
        .margin({ left: Constants.MARGIN_SMALL })
        .onClick(() => {
          if (this.inputText.trim()) {
            this.sendMessage(this.inputText);
            this.inputText = '';
          }
        })
    }
    .width('100%')
    .padding(Constants.PADDING_MEDIUM)
    .backgroundColor(Constants.WHITE)
    .border({
      width: { top: 1 },
      color: Constants.BORDER_COLOR
    })
  }

  private initializeChat() {
    this.messages = [
      {
        id: '1',
        content: '你好！我是您的健康助手，我可以帮助您分析健康数据，分享健康建议。有什么可以帮助您的吗？',
        type: 'assistant',
        time: DateUtils.getCurrentTime()
      },
      {
        id: '2',
        content: '我想了解一下我最近的健康状况',
        type: 'user',
        time: DateUtils.getCurrentTime()
      },
      {
        id: '3',
        content: '根据您的健康数据，我为您提供了一个详细的分析：',
        type: 'assistant',
        time: DateUtils.getCurrentTime(),
        isChart: true,
        chartData: {}
      }
    ];
  }

  private sendMessage(content: string) {
    // 添加用户消息
    this.messages.push({
      id: Date.now().toString(),
      content: content,
      type: 'user',
      time: DateUtils.getCurrentTime()
    });

    // 模拟AI回复
    this.isLoading = true;
    setTimeout(() => {
      this.isLoading = false;
      this.messages.push({
        id: (Date.now() + 1).toString(),
        content: this.generateResponse(content),
        type: 'assistant',
        time: DateUtils.getCurrentTime()
      });
    }, 1500);
  }

  private generateResponse(userMessage: string): string {
    if (userMessage.includes('健康咨询')) {
      return '我可以为您提供健康咨询服务。请告诉我您的具体症状或健康问题，我会根据您的情况给出专业建议。';
    } else if (userMessage.includes('用药')) {
      return '关于用药问题，建议您咨询专业医生。我可以提供一般性的用药知识，但具体用药方案需要医生根据您的情况制定。';
    } else if (userMessage.includes('运动')) {
      return '根据您的健康状况，建议您进行适量的有氧运动，如散步、游泳等。每周至少150分钟中等强度运动。';
    } else if (userMessage.includes('饮食')) {
      return '建议您保持均衡饮食，多吃蔬菜水果，控制盐分摄入，避免高脂肪食物。具体饮食计划可以咨询营养师。';
    } else {
      return '感谢您的提问。我会根据您的健康数据为您提供个性化的建议。如需更详细的分析，请提供更多信息。';
    }
  }

  private resetChat() {
    this.messages = [];
    this.initializeChat();
  }
}
